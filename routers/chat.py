import time
import asyncio
from datetime import datetime, timezone
from fastapi import APIRouter, Request
from http import HTTPStatus
from fastapi.responses import JSONResponse, StreamingResponse
from schemas.chat import ChatMessageRequest
from utils.collections import generate_collection_id
from utils.logger import logger
import json
import os
from uuid import uuid4
from services.chat import Chat
from services.service_client import ServiceClient
from services.geminiai import initialize_client
from google.genai import types
from cachetools import TTLCache

chat_router = APIRouter(
    prefix="/chat/v1",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)

# Load and validate env vars
_raw = {
    "MODEL_ID": os.getenv("MODEL_ID"),
    "DB_NAME": os.getenv("DB_NAME"),
    "MAX_CACHE": os.getenv("MAX_CACHE_SIZE", "1024"),
    "MAX_TTL": os.getenv("MAX_TTL", "3600.0"),
    "TEMPERATURE": os.getenv("TEMPERATURE", "0.45"),
    "TOP_K": os.getenv("TOP_K", "40"),
    "TOP_P": os.getenv("TOP_P", "1.0"),
}
if not _raw["MODEL_ID"]:
    logger.error("Missing required environment variable: MODEL_ID")
    raise RuntimeError("MODEL_ID is required")
if not _raw["DB_NAME"]:
    logger.error("Missing required environment variable: DB_NAME")
    raise RuntimeError("DB_NAME is required")

def _to_int(name: str, val: str) -> int:
    try:
        return int(val)
    except ValueError:
        raise ValueError(f"{name} must be an integer (got {val!r})")

def _to_float(name: str, val: str) -> float:
    try:
        return float(val)
    except ValueError:
        raise ValueError(f"{name} must be a float (got {val!r})")

MODEL_ID = _raw["MODEL_ID"]
DB_NAME = _raw["DB_NAME"]
MAX_CACHE_SIZE = _to_int("MAX_CACHE_SIZE", _raw["MAX_CACHE"])
MAX_TTL         = _to_float("MAX_TTL", _raw["MAX_TTL"])
TEMPERATURE     = _to_float("TEMPERATURE", _raw["TEMPERATURE"])
TOP_K           = _to_int("TOP_K", _raw["TOP_K"])
TOP_P           = _to_float("TOP_P", _raw["TOP_P"])

app = ServiceClient().app
active_chats: TTLCache = TTLCache(maxsize=MAX_CACHE_SIZE, ttl=MAX_TTL)
config = types.GenerateContentConfig(
    temperature=TEMPERATURE,
    top_k=TOP_K,
    top_p=TOP_P,
)

def sse_format(event: dict) -> str:
    return f"data: {json.dumps(event, ensure_ascii=False)}\n\n"

def current_utc_time_str() -> str:
    """Return current UTC time formatted as YYYY-MM-DD HH:MM:SS UTC."""
    return datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")

async def handle_chat(request: ChatMessageRequest, context: Request):
    logger.info("Handling chat with query: %s", request.query)
    client = initialize_client()

    # Retrieve or create chat session
    if request.chat_id and request.chat_id in active_chats:
        chat_session = active_chats[request.chat_id]
    else:
        chat_session = client.chats.create(model=MODEL_ID, config=config, history=[])
        request.chat_id = str(uuid4())
        active_chats[request.chat_id] = chat_session

    # Retrieve context passages
    collection_id = generate_collection_id(f"{request.tenant_id}_{request.role_id}")
    t0 = time.perf_counter()
    db = await context.app.state.vector_storage.get_chroma_db(collection_id=collection_id)
    passage = await context.app.state.vector_storage.get_relevant_passage_from_db(request.query, db)
    retrieval_ms = (time.perf_counter() - t0) * 1000
    logger.info("Chroma retrieval took %.1fms", retrieval_ms)

    # Build prompt
    relevant_passage = passage or ""
    if relevant_passage:
        logger.info("Relevant passage length: %d chars", len(relevant_passage))
    config.system_instruction = Chat().make_prompt(
        query=request.query,
        relevant_passage=relevant_passage,
        role_id=request.role_id
    )

    # Log streaming start
    stream_start_wall = current_utc_time_str()
    logger.info("=== Streaming started at %s ===", stream_start_wall)
    stream_start_perf = time.perf_counter()

    first_token = True
    # Stream tokens
    for chunk in chat_session.send_message_stream(request.query, config=config):
        now = time.perf_counter()
        if first_token:
            logger.info("Time to first token: %.1fms", (now - stream_start_perf) * 1000)
            first_token = False
        yield sse_format({"chat_id": request.chat_id, "delta": chunk.text})

    # Log streaming end
    total_stream_ms = (time.perf_counter() - stream_start_perf) * 1000
    stream_end_wall = current_utc_time_str()
    logger.info("Total stream time: %.1fms", total_stream_ms)
    logger.info("=== Streaming ended at %s ===", stream_end_wall)

    yield "data: [DONE]\n\n"

@chat_router.post("/start")
async def chat(request: ChatMessageRequest, context: Request):
    if not request.query:
        return JSONResponse(
            content={"error": "Query is required."},
            status_code=HTTPStatus.BAD_REQUEST,
        )
    return StreamingResponse(
        handle_chat(request, context),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )
