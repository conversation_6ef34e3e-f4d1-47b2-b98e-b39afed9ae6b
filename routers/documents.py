from fastapi import APIRouter, Request, Depends, Security, HTTPException, status
from fastapi.security.api_key import API<PERSON>eyHeader
from pydantic import BaseModel
import urllib.parse
import os

from services.documents import Documents
from utils.collections import generate_collection_id
from utils.logger import logger


API_KEY_NAME = "X-LOAD-API-KEY"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

async def verify_load_key(key: str = Security(api_key_header)):
    expected = os.getenv("LOAD_JOB_API_KEY")
    if not key or key != expected:
        logger.warning("Unauthorized load job attempt.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid or missing API key for load job",
        )


documents_router = APIRouter(
    prefix="/documents",
    tags=["documents"],
    responses={404: {"description": "Not found"}},
)

documentService = Documents()

class DocumentToVectorise(BaseModel, extra="allow"):
    document_content: bytes | None
    file_type: str
    document_embedding: str
    document_url: str  

def get_document(document: str) -> DocumentToVectorise | None:
    """
    Downloads a doc; skips images silently; returns only PDF/DOCX.
    """
    parsed = urllib.parse.urlparse(document)
    path = parsed.path.lower()
    if path.endswith((".png", ".jpg", ".jpeg")):
        return None

    file_type = documentService._get_file_type(document)
    if not file_type:
        logger.debug(f"Skipping non‑PDF/DOCX: {document}")
        return None

    content = documentService.download_document(document)
    if not content:
        logger.warning(f"Download failed: {document}")
        return None

    return DocumentToVectorise(
        document_content=content,
        file_type=file_type,
        document_embedding="",
        document_url=document,
    )

async def load_results(context: Request) -> bool:
    """
    Loads & vectorizes ALL documents. Logs file lists and a two-sentence sample per file.
    """
    metadata = documentService.get_documents()
    groups: dict[str, list[str]] = {}
    success = True

    for item in metadata:
        t, r, urls = item.get("tenant_id"), item.get("role"), item.get("documents", [])
        if not (t and r and urls):
            logger.warning(f"Incomplete metadata: {item}")
            continue
        key = f"{t}_{r}"
        groups.setdefault(key, []).extend(urls)

    for key, urls in groups.items():
        logger.info(f"Processing group: '{key}'")
        pdfs, docsx = [], []
        to_process: list[DocumentToVectorise] = []

        for u in urls:
            doc = get_document(u)
            if not doc:
                continue
            if doc.file_type == "pdf":
                pdfs.append(u)
            elif doc.file_type == "docx":
                docsx.append(u)
            to_process.append(doc)

        def names(urls: list[str]):
            return [os.path.basename(urllib.parse.urlparse(u).path) for u in urls]

        if pdfs:
            logger.info(f"PDF files in '{key}': {names(pdfs)}")
        if docsx:
            logger.info(f"DOCX files in '{key}': {names(docsx)}")

        if not to_process:
            logger.info(f"No valid docs for '{key}'. Skipping.")
            continue

        texts: list[str] = []
        for doc in to_process:
            try:
                text = documentService.generate_embeddings(doc.document_content, doc.file_type)  # type: ignore
                if not text.strip():
                    logger.warning(f"Empty text for '{doc.document_url}'")
                    continue

                sents = [s.strip() for s in text.replace("\n", " ").split(".") if s.strip()]
                if sents:
                    sample = ". ".join(sents[:2])
                    filename = os.path.basename(urllib.parse.urlparse(doc.document_url).path)
                    logger.info(f"Sample from '{filename}': {sample}")

                texts.append(text)

            except Exception as e:
                logger.error(f"Error parsing '{doc.document_url}': {e}")

        if not texts:
            logger.info(f"No text extracted for '{key}'. Skipping.")
            continue

        vect = context.app.state.vector_storage
        coll = generate_collection_id(key)
        res, err = await vect.create_chroma_db(texts, coll)
        if res:
            logger.info(f"Chroma reloaded for '{coll}'")
        else:
            logger.error(f"Failed reload for '{coll}': {err}")
            success = False

    return success


@documents_router.post(
    "/load",
    dependencies=[Depends(verify_load_key)],  
)
async def load_documents(context: Request):
    logger.info("Received request to /documents/load")
    if await load_results(context):
        return {"message": "Document embeddings loaded successfully."}
    else:
        return {"message": "Failed to load document embeddings. Check logs."}

@documents_router.get("/health")
async def health_check():
    return {"status": "ok"}
