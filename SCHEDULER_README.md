# Document Loading Scheduler

## Overview

The application now includes an automated scheduler that runs the document loading process daily at 12:00 PM Asia/Kolkata time using APScheduler.

## Features

- **Daily Execution**: Automatically loads and vectorizes documents every day at 12:00 PM
- **Timezone Support**: Uses Asia/Kolkata timezone as configured in the application
- **Error Handling**: Comprehensive logging and error handling for scheduled jobs
- **Non-blocking**: Runs asynchronously without affecting the main FastAPI application

## Implementation Details

### Dependencies Added
- `apscheduler ^3.11.0` - Advanced Python Scheduler
- `tzlocal 5.3.1` - Timezone handling (automatically installed with APScheduler)

### Code Changes

1. **routers/documents.py**:
   - Extracted core logic into `load_documents_core(vector_storage)` function
   - Maintained backward compatibility with existing HTTP endpoint
   - Removed dependency on FastAPI Request object for scheduled execution

2. **main.py**:
   - Added APScheduler imports and configuration
   - Integrated scheduler into FastAPI lifespan management
   - Configured daily job with proper timezone handling
   - Added graceful scheduler shutdown

### Scheduler Configuration

```python
# Runs daily at 12:00 PM Asia/Kolkata time
scheduler.add_job(
    scheduled_load_documents,
    CronTrigger(hour=12, minute=0, timezone=asia_kolkata),
    id='daily_document_load',
    name='Daily Document Loading',
    replace_existing=True
)
```

## Environment Variables

The scheduler uses the same environment variables as the existing document loading functionality:

- `BASE_URL` - API endpoint for document retrieval
- `API_KEY` - Authentication key for document API
- `LOAD_JOB_API_KEY` - API key for load job authentication (used by HTTP endpoint)
- `VERTEX_AI_SA_API` - Service account JSON for Vertex AI (properly formatted)

## Monitoring

The scheduler logs all activities:

- **Startup**: "Scheduler started. Documents will be loaded daily at 12:00 Asia/Kolkata time."
- **Execution**: "Starting scheduled document loading job..."
- **Success**: "Scheduled document loading completed successfully."
- **Errors**: "Error in scheduled document loading: {error_details}"
- **Shutdown**: "Scheduler shutdown completed."

## Manual Execution

The existing HTTP endpoint `/documents/load` remains available for manual document loading:

```bash
curl -X POST "http://localhost:8000/documents/load" \
  -H "X-LOAD-API-KEY: your_load_job_api_key"
```

## Testing

The scheduler has been tested and verified to:
- Start correctly with the FastAPI application
- Execute scheduled jobs at the correct time
- Handle errors gracefully
- Shutdown properly when the application stops

## Troubleshooting

1. **Scheduler not starting**: Check logs for import errors or configuration issues
2. **Jobs not executing**: Verify timezone configuration and system time
3. **Document loading failures**: Check the same logs as manual document loading
4. **Memory issues**: Monitor application memory usage during scheduled runs

## Future Enhancements

Potential improvements could include:
- Configurable schedule timing via environment variables
- Multiple schedule options (hourly, weekly, etc.)
- Job status monitoring endpoint
- Retry mechanisms for failed executions
