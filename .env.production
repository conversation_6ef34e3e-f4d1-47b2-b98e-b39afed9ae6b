# Production Environment Configuration
# Copy this to .env for production environment

MODEL_ID = "gemini-2.5-flash-lite-preview-06-17"
MAX_CACHE_SIZE = 1024
MAX_TTL = 3600

# Model parameters
TEMPERATURE= 0.45
TOP_K= 40
TOP_P= 0.1

# Document API request - Production
BASE_URL="https://core-prod.effihr.com/internal"
API_KEY="YOUR_PRODUCTION_API_KEY_HERE"
ACCEPT_LANGUAGE="en-IN,en-US;q=0.9,en;q=0.8"

# GenAI API key 
GENAI_API_KEY="YOUR_PRODUCTION_GENAI_API_KEY_HERE"
GCP_PROJECT_ID="effihr-ai"
GCP_LOCATION ="global"

# Database
DB_NAME = "YOUR_PRODUCTION_DB_NAME_HERE"
EMBEDDING_MODEL_ID=  "gemini-embedding-001"

# Vertex AI Service Account - Use production service account
VERTEX_AI_SA_API = 'YOUR_PRODUCTION_VERTEX_AI_SA_JSON_HERE'

EMBEDDING_LOCATION = "asia-south1"
LOAD_JOB_API_KEY = 'YOUR_PRODUCTION_LOAD_JOB_API_KEY_HERE'
