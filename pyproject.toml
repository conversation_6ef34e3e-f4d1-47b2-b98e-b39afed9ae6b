[tool.poetry]
name = "effi-rag"
version = "0.1.0"
description = "This project is for rag integration with different LLMs"
authors = ["<EMAIL>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10"
fastapi = ">=0.115.9,<0.116.0"
openai = ">=1.72.0,<2.0.0"
pypdf = ">=5.4.0,<6.0.0"
python-docx = ">=1.1.2,<2.0.0"
requests = ">=2.32.3,<3.0.0"
numpy = ">=2.2.4,<3.0.0"
uvicorn = ">=0.34.0,<0.35.0"
google-genai = ">=1.10.0,<2.0.0"
chromadb = ">=1.0.4,<2.0.0"
python-dotenv = ">=1.1.0,<2.0.0"
pyyaml = ">=6.0.2,<7.0.0"
pytz = ">=2025.2,<2026.0"
apscheduler = "^3.11.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
